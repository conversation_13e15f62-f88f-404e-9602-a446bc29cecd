//
//  Coordinator.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 17/04/25.
//

import Foundation
import SwiftUI
import SwiftData

enum CreatorTab: String, CaseIterable, Identifiable {
    case overview = "Overview"
    case videos = "Videos"
    case playlists = "Playlists"
    case drafts = "Drafts"
    case aiScriptWriter = "AI Script Writer"
//    case uploadQueue = "Upload Videos"
    case uploadVideos = "Upload Video"
//    case aiScriptWriter = "AI Script Writer"

    var id: String { self.rawValue }

    var iconName: String {
        switch self {
        case .overview: return "house.fill"
        case .videos: return "film.fill"
        case .playlists: return "music.note.list"
        case .drafts: return "doc.text.fill"
        case .uploadVideos: return "arrow.up.circle.fill"
        case .aiScriptWriter: return "pencil"
        }
    }
}


final class Coordinator: ObservableObject {
    @Published var selectedTab: CreatorTab = .overview
    @Published var selectedDraft: ProjectDraft?
    
    @ViewBuilder
    func viewForSelectedTab(using navigationCoordinator: NavigationCoordinator) -> some View {
        switch selectedTab {
        case .overview:
            OverviewTab().environmentObject(navigationCoordinator)
        case .videos:
            UserVideosView().environmentObject(navigationCoordinator)
        case .playlists:
            PlayListsView().environmentObject(navigationCoordinator)
        case .drafts:
            DraftsView().environmentObject(navigationCoordinator)
        case .uploadVideos:
            VideosUploadView(draftToLoad: selectedDraft).environmentObject(navigationCoordinator)
        case .aiScriptWriter:
            ScriptWriterView().environmentObject(navigationCoordinator)
        }
    }

    // MARK: - Navigation Methods
    func navigateToVideoUploadPage() {
        selectedDraft = nil
        selectedTab = .uploadVideos
    }

    func navigateToVideoUploadPage(with draft: ProjectDraft) {
        selectedDraft = draft
        selectedTab = .uploadVideos
    }

    func navigateToNewTab() {
        selectedTab = .uploadVideos
    }
}
