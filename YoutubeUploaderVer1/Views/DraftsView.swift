//
//  DraftsView.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 18/06/25.
//

import SwiftUI
import SwiftData

struct DraftsView: View {
    @Environment(\.modelContext) private var modelContext
    @StateObject private var draftService = DraftManagementService.shared
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    @State private var selectedDrafts: Set<ProjectDraft> = []
    @State private var showDeleteConfirmation = false
    @State private var searchText = ""
    
    // Grid layout
    private let columns = [
        GridItem(.adaptive(minimum: 320, maximum: 400), spacing: 20)
    ]
    
    var filteredDrafts: [ProjectDraft] {
        if searchText.isEmpty {
            return draftService.drafts
        } else {
            return draftService.drafts.filter { draft in
                draft.projectName.localizedCaseInsensitiveContains(searchText) ||
                draft.title.localizedCaseInsensitiveContains(searchText) ||
                draft.description.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Modern Header Section
            modernHeaderSection
            
            // Content Section
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    if draftService.isLoading {
                        loadingSection
                    } else if filteredDrafts.isEmpty {
                        emptyStateSection
                    } else {
                        draftsGridSection
                    }
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 24)
            }
        }
        .onAppear {
            draftService.setModelContext(modelContext)
        }
        .alert("Delete Drafts", isPresented: $showDeleteConfirmation) {
            Button("Delete", role: .destructive) {
                draftService.deleteDrafts(Array(selectedDrafts))
                selectedDrafts.removeAll()
            }
            Button("Cancel", role: .cancel) {}
        } message: {
            Text("Are you sure you want to delete \(selectedDrafts.count) draft\(selectedDrafts.count == 1 ? "" : "s")? This action cannot be undone.")
        }
    }
    
    // MARK: - Modern Header Section
    private var modernHeaderSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack(alignment: .top) {
                // Title and Subtitle
                VStack(alignment: .leading, spacing: 8) {
                    Text("Project Drafts")
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [AppColor.textPrimary.color, AppColor.accentBlue.color],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                    
                    Text("Manage your saved video projects and drafts")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Action buttons
                HStack(spacing: 12) {
                    if !selectedDrafts.isEmpty {
                        Button {
                            showDeleteConfirmation = true
                        } label: {
                            HStack(spacing: 8) {
                                Image(systemName: "trash")
                                    .font(.system(size: 14, weight: .medium))
                                Text("Delete (\(selectedDrafts.count))")
                                    .font(.system(size: 14, weight: .medium))
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(AppColor.youtubeRed.color)
                            .cornerRadius(8)
                        }
                        .buttonStyle(.plain)
                    }
                    
                    Button {
                        draftService.clearOldDrafts()
                    } label: {
                        HStack(spacing: 8) {
                            Image(systemName: "clock.arrow.circlepath")
                                .font(.system(size: 14, weight: .medium))
                            Text("Clear Old")
                                .font(.system(size: 14, weight: .medium))
                        }
                        .foregroundColor(AppColor.textSecondary.color)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(AppColor.surfaceSecondary.color)
                        .cornerRadius(8)
                    }
                    .buttonStyle(.plain)
                }
            }
            
            // Search and Stats
            HStack(spacing: 16) {
                // Search bar
                HStack(spacing: 12) {
                    Image(systemName: "magnifyingglass")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                    
                    TextField("Search drafts...", text: $searchText)
                        .textFieldStyle(.plain)
                        .font(.system(size: 14, weight: .medium))
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(AppColor.surfaceSecondary.color)
                .cornerRadius(10)
                
                Spacer()
                
                // Stats
                HStack(spacing: 16) {
                    statsItem(title: "Total", value: "\(draftService.drafts.count)")
                    if !searchText.isEmpty {
                        statsItem(title: "Filtered", value: "\(filteredDrafts.count)")
                    }
                }
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 20)
        .background(
            LinearGradient(
                colors: [
                    AppColor.surfacePrimary.color.opacity(0.8),
                    AppColor.surfaceSecondary.color.opacity(0.4)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .overlay(
            Rectangle()
                .fill(AppColor.borderPrimary.color.opacity(0.2))
                .frame(height: 1),
            alignment: .bottom
        )
    }
    
    private func statsItem(title: String, value: String) -> some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(AppColor.textPrimary.color)
            Text(title)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(AppColor.textSecondary.color)
        }
    }
    
    // MARK: - Loading Section
    private var loadingSection: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .progressViewStyle(CircularProgressViewStyle(tint: AppColor.accentBlue.color))
            
            Text("Loading drafts...")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(AppColor.textSecondary.color)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
    }
    
    // MARK: - Empty State Section
    private var emptyStateSection: some View {
        VStack(spacing: 24) {
            // Empty state icon
            ZStack {
                Circle()
                    .fill(AppColor.surfaceSecondary.color)
                    .frame(width: 120, height: 120)
                
                Image(systemName: "doc.text")
                    .font(.system(size: 48, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
            }
            
            VStack(spacing: 12) {
                Text(searchText.isEmpty ? "No Drafts Yet" : "No Matching Drafts")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)
                
                Text(searchText.isEmpty ? 
                     "Start creating video projects to see your drafts here" :
                     "Try adjusting your search terms")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
            
            if searchText.isEmpty {
                Button {
                    // Navigate to upload videos tab
                    navigationCoordinator.navigateToVideoUploadPage()
                } label: {
                    HStack(spacing: 10) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 16, weight: .semibold))
                        Text("Create New Project")
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 14)
                    .background(
                        LinearGradient(
                            colors: [AppColor.accentBlue.color, AppColor.accentBlue.color.opacity(0.8)],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
                    .shadow(color: AppColor.accentBlue.color.opacity(0.3), radius: 8, x: 0, y: 4)
                }
                .buttonStyle(.plain)
            }
        }
        .frame(maxWidth: .infinity, minHeight: 400)
    }
    
    // MARK: - Drafts Grid Section
    private var draftsGridSection: some View {
        LazyVGrid(columns: columns, spacing: 20) {
            ForEach(filteredDrafts, id: \.id) { draft in
                DraftCard(
                    draft: draft,
                    isSelected: selectedDrafts.contains(draft),
                    onSelect: { isSelected in
                        if isSelected {
                            selectedDrafts.insert(draft)
                        } else {
                            selectedDrafts.remove(draft)
                        }
                    },
                    onTap: {
                        // Navigate to upload view with this draft
                        navigationCoordinator.navigateToVideoUploadPage(with: draft)
                    },
                    onDelete: {
                        draftService.deleteDraft(draft)
                    }
                )
            }
        }
    }
}
